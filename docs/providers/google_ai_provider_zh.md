# Google AI 提供者 - 使用说明

## 概述

Google AI 提供者是基于最新 Gemini API 规范的完整实现，支持 Google 最先进的生成式 AI 模型。本模块已更新以支持 Gemini 2.5 和 2.0 系列的最新功能。

## 支持的模型

### Gemini 2.5 系列（推荐）

| 模型名称 | 描述 | 最佳用途 |
|---------|------|---------|
| `gemini-2.5-pro` | 最先进的推理和编码能力 | 复杂推理、代码生成、数据分析 |
| `gemini-2.5-flash` | 平衡性能和成本的最佳选择 | 通用任务、高吞吐量应用 |
| `gemini-2.5-flash-lite` | 成本效益最高的轻量级模型 | 简单任务、实时应用 |

### Gemini 2.0 系列

| 模型名称 | 描述 | 最佳用途 |
|---------|------|---------|
| `gemini-2.0-flash` | 超快速度和原生工具使用 | 需要快速响应的应用 |
| `gemini-2.0-flash-lite` | 2.0 系列的轻量级版本 | 成本敏感的快速任务 |

### 向后兼容模型

为保持向后兼容性，以下旧模型名称会自动映射到最新的对应模型：

- `gemini-pro` → `gemini-2.5-pro`
- `gemini-pro-vision` → `gemini-2.5-pro`
- `gemini-1.0-pro` → `gemini-2.5-pro`

## 主要功能

### 1. 多模态输入支持

支持以下输入类型：
- **文本**：普通文本消息
- **图像**：JPEG、PNG、WebP 格式
- **音频**：WAV、MP3、FLAC 格式
- **视频**：MP4、MOV、AVI 格式
- **PDF 文档**：文档理解和分析

### 2. 系统指令

正确使用 Gemini API 的 `system_instruction` 字段：

```python
from ai_gen_hub.core.interfaces import TextGenerationRequest, Message, MessageRole

request = TextGenerationRequest(
    model="gemini-2.5-flash",
    messages=[
        Message(role=MessageRole.SYSTEM, content="你是一个专业的编程助手。"),
        Message(role=MessageRole.USER, content="请解释什么是递归？")
    ]
)
```

### 3. Thinking 配置（2.5 系列专用）

Gemini 2.5 系列模型支持"思考"功能，可以显示模型的推理过程：

```python
# 启用思考功能（默认）
request = TextGenerationRequest(
    model="gemini-2.5-flash",
    messages=[...],
    # thinking_budget 默认为 None，使用模型默认值
)

# 禁用思考功能
request = TextGenerationRequest(
    model="gemini-2.5-flash", 
    messages=[...],
    thinking_budget=0  # 设置为 0 禁用思考
)

# 自定义思考预算
request = TextGenerationRequest(
    model="gemini-2.5-flash",
    messages=[...],
    thinking_budget=1000  # 设置思考 token 预算
)
```

### 4. 流式响应

支持实时流式输出：

```python
async def stream_example():
    request = TextGenerationRequest(
        model="gemini-2.5-flash",
        messages=[Message(role=MessageRole.USER, content="写一个故事")],
        stream=True
    )
    
    async for chunk in provider.generate_text(request):
        print(chunk.choices[0].delta.content, end="")
```

### 5. 安全设置

可配置的内容安全过滤：

```python
# 自定义安全设置
safety_settings = [
    {
        "category": "HARM_CATEGORY_HARASSMENT",
        "threshold": "BLOCK_LOW_AND_ABOVE"  # 更严格的过滤
    },
    {
        "category": "HARM_CATEGORY_HATE_SPEECH",
        "threshold": "BLOCK_NONE"  # 禁用此类别的过滤
    }
]

request = TextGenerationRequest(
    model="gemini-2.5-flash",
    messages=[...],
    safety_settings=safety_settings
)
```

### 6. 结构化输出

支持 JSON 格式响应：

```python
request = TextGenerationRequest(
    model="gemini-2.5-flash",
    messages=[Message(role=MessageRole.USER, content="生成一个用户信息的 JSON")],
    response_format={"type": "json_object"}
)
```

## 配置选项

### 基础配置

```python
from ai_gen_hub.config.settings import ProviderConfig

config = ProviderConfig(
    name="google_ai",
    api_key="your-api-key",
    base_url="https://generativelanguage.googleapis.com/v1beta",  # 可选，使用默认值
    timeout=30,  # 请求超时时间
    max_retries=3  # 最大重试次数
)
```

### 生成参数

| 参数 | 类型 | 描述 | 默认值 |
|------|------|------|--------|
| `temperature` | float | 控制输出的随机性 (0.0-2.0) | 1.0 |
| `top_p` | float | 核采样参数 (0.0-1.0) | None |
| `top_k` | int | Top-K 采样参数 | None |
| `max_tokens` | int | 最大输出 token 数 | None |
| `stop` | str/list | 停止序列 | None |

## 错误处理

### 常见错误类型

1. **API 密钥无效**
   ```
   错误：401 Unauthorized
   解决：检查 API 密钥是否正确设置
   ```

2. **配额超限**
   ```
   错误：429 Too Many Requests
   解决：检查 API 配额或实现请求限流
   ```

3. **内容被过滤**
   ```
   错误：finish_reason = "content_filter"
   解决：调整安全设置或修改输入内容
   ```

4. **模型不存在**
   ```
   错误：404 Not Found
   解决：检查模型名称是否正确
   ```

### 重试机制

提供者内置了智能重试机制：
- 自动重试临时性错误（网络超时、服务器错误）
- 指数退避策略
- 可配置的最大重试次数

## 性能优化建议

### 1. 模型选择

- **高质量任务**：使用 `gemini-2.5-pro`
- **平衡性能**：使用 `gemini-2.5-flash`（推荐）
- **高吞吐量**：使用 `gemini-2.5-flash-lite`

### 2. 批处理

对于大量请求，考虑使用批处理 API（如果可用）。

### 3. 缓存

对于重复的系统指令或上下文，考虑使用上下文缓存功能。

### 4. 流式处理

对于长文本生成，使用流式响应提升用户体验。

## 示例代码

### 基础文本生成

```python
import asyncio
from ai_gen_hub.providers.google_ai_provider import GoogleAIProvider
from ai_gen_hub.config.settings import ProviderConfig
from ai_gen_hub.utils.key_manager import KeyManager
from ai_gen_hub.core.interfaces import TextGenerationRequest, Message, MessageRole

async def basic_example():
    # 配置提供者
    config = ProviderConfig(name="google_ai", api_key="your-api-key")
    key_manager = KeyManager()
    provider = GoogleAIProvider(config, key_manager)
    
    # 创建请求
    request = TextGenerationRequest(
        model="gemini-2.5-flash",
        messages=[
            Message(role=MessageRole.USER, content="解释量子计算的基本原理")
        ],
        temperature=0.7,
        max_tokens=1000
    )
    
    # 生成响应
    response = await provider.generate_text(request)
    print(response.choices[0].message.content)

# 运行示例
asyncio.run(basic_example())
```

### 多轮对话

```python
async def chat_example():
    config = ProviderConfig(name="google_ai", api_key="your-api-key")
    key_manager = KeyManager()
    provider = GoogleAIProvider(config, key_manager)
    
    messages = [
        Message(role=MessageRole.SYSTEM, content="你是一个友好的助手。"),
        Message(role=MessageRole.USER, content="你好！")
    ]
    
    request = TextGenerationRequest(
        model="gemini-2.5-flash",
        messages=messages
    )
    
    response = await provider.generate_text(request)
    assistant_message = response.choices[0].message.content
    print(f"助手：{assistant_message}")
    
    # 继续对话
    messages.append(Message(role=MessageRole.ASSISTANT, content=assistant_message))
    messages.append(Message(role=MessageRole.USER, content="能告诉我今天的天气吗？"))
    
    request.messages = messages
    response = await provider.generate_text(request)
    print(f"助手：{response.choices[0].message.content}")

asyncio.run(chat_example())
```

## 更新日志

### 2025-08-13 - 重大更新

- ✅ 添加 Gemini 2.5 Pro、2.5 Flash、2.5 Flash-Lite 支持
- ✅ 添加 Gemini 2.0 Flash 系列支持
- ✅ 实现 Thinking 配置支持
- ✅ 改进系统指令处理（使用 system_instruction 字段）
- ✅ 增强流式响应处理
- ✅ 更新安全设置和错误处理
- ✅ 添加结构化输出支持
- ✅ 完善向后兼容性

### 已知限制

- 图像生成功能尚未实现（计划在未来版本中添加）
- 某些预览版模型可能有使用限制
- 1.5 系列模型将于 2025 年 9 月弃用

## 技术支持

如有问题或建议，请：
1. 查看 [Google AI 官方文档](https://ai.google.dev/gemini-api/docs)
2. 检查 API 密钥和配额状态
3. 查看日志输出获取详细错误信息
4. 提交 Issue 到项目仓库
